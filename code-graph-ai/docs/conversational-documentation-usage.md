# 多轮对话式文档生成使用指南

## 概述

多轮对话式文档生成是对原有分批处理策略的重大改进，通过利用Spring AI的记忆功能，实现了更智能、更连贯的文档生成过程。

## 核心特性

### 1. 智能模式选择
系统会根据以下因素自动选择使用传统并行模式还是多轮对话模式：
- **层级复杂度**：第1层使用传统模式，第2、3层推荐对话模式
- **内容规模**：方法数超过15个或内容大小超过50KB时使用对话模式
- **配置开关**：可通过配置完全启用或禁用对话模式

### 2. 上下文记忆管理
- 使用Spring AI的`MessageChatMemoryAdvisor`管理对话历史
- 支持可配置的记忆窗口大小（默认20条消息）
- 自动清理机制避免内存泄漏

### 3. 渐进式文档构建
- 每轮对话都基于之前的分析结果
- AI能够理解整体架构并避免重复内容
- 最终整合生成完整、连贯的技术文档

## 使用方式

### 基本用法

```java
@Autowired
private BatchDocumentationService batchDocumentationService;

// 创建文档生成上下文
DocumentationGenerationContext context = DocumentationGenerationContext.builder()
    .entryPointId("com.example.service.UserService.createUser")
    .level(2)
    .subgraph(subgraphData)
    .projectId("user-management-system")
    .branchName("feature/user-registration")
    .build();

// 生成文档（自动选择最佳模式）
String documentation = batchDocumentationService.generateBatchDocumentation(context);
```

### 强制使用对话模式

```java
// 直接调用对话模式
String documentation = batchDocumentationService.generateConversationalDocumentation(context);
```

### 强制使用传统模式

```java
// 直接调用传统模式
String documentation = batchDocumentationService.generateTraditionalBatchDocumentation(context);
```

## 配置选项

### 对话模式配置

```java
// 检查是否启用对话模式
boolean enabled = ConversationalDocumentationConfig.isConversationalModeEnabled();

// 获取最大对话轮数
int maxRounds = ConversationalDocumentationConfig.getMaxConversationRounds();

// 获取记忆窗口大小
int windowSize = ConversationalDocumentationConfig.getMemoryWindowSize();

// 获取建议的批次数
int batchCount = ConversationalDocumentationConfig.getSuggestedBatchCount(context);
```

### 自定义判断逻辑

```java
// 基于层级判断
boolean useForLevel = ConversationalDocumentationConfig.shouldUseConversationalForLevel(2);

// 基于内容复杂度判断
boolean useForContent = ConversationalDocumentationConfig.shouldUseConversationalForContent(20, 60000);

// 综合判断
boolean shouldUse = ConversationalDocumentationConfig.shouldUseConversationalMode(context);
```

## 工作流程

### 1. 模式选择阶段
```
输入：DocumentationGenerationContext
↓
检查配置和内容复杂度
↓
选择：对话模式 or 传统模式
```

### 2. 对话模式流程
```
1. 生成唯一对话ID
2. 初始化对话（设置任务背景）
3. 分批处理：
   - 第1轮：分析第一批代码
   - 第2轮：基于前面分析，处理第二批
   - ...
   - 第N轮：基于所有历史，处理最后一批
4. 最终整合：生成完整文档
5. 清理对话历史
```

### 3. 传统模式流程
```
1. 分批构建提示词
2. 并行调用AI服务
3. 收集所有批次结果
4. 简单合并生成文档
```

## 提示词模板

### 初始化提示词
系统会自动生成包含以下信息的初始化提示词：
- 项目基本信息（入口点、层级、方法数等）
- 任务说明和要求
- 文档格式规范

### 轮次分析提示词
每轮对话包含：
- 当前轮次信息（第X轮，共Y轮）
- 具体的代码内容
- 分析要求和输出格式
- 与整体架构的关系说明

### 最终整合提示词
要求AI基于所有历史对话：
- 整合所有分析内容
- 生成结构化的完整文档
- 确保逻辑连贯性

## 性能考虑

### 优势
- **内容质量**：AI能够理解全局上下文，生成更连贯的文档
- **避免重复**：基于记忆的分析避免内容重复
- **智能整合**：最终文档结构更合理

### 权衡
- **处理时间**：串行处理比并行处理耗时更长
- **内存使用**：需要维护对话历史
- **API调用**：可能产生更多的AI服务调用

### 适用场景
- **推荐使用对话模式**：
  - 复杂的第2、3层调用关系
  - 方法数量较多（>15个）
  - 需要高质量文档的场景

- **推荐使用传统模式**：
  - 简单的第1层调用关系
  - 方法数量较少（<10个）
  - 对处理速度要求较高的场景

## 监控和调试

### 日志输出
系统提供详细的日志输出：
```
INFO  - 开始多轮对话式生成第2层文档，入口点: com.example.Service.method
INFO  - 将第2层文档分为 4 轮对话处理
INFO  - 开始第 1 轮对话 (共 4 轮)
INFO  - 完成第 1 轮对话，内容长度: 1250 字符
...
INFO  - 完成第2层多轮对话文档生成，最终内容长度: 5680 字符
```

### 对话历史管理
- 每个文档生成任务使用唯一的对话ID
- 支持对话历史的查看和调试
- 可配置的自动清理机制

## 扩展和定制

### 自定义提示词模板
可以通过继承`ConversationalPromptBuilder`来定制提示词：

```java
@Component
public class CustomConversationalPromptBuilder extends ConversationalPromptBuilder {
    
    @Override
    public String buildInitializationPrompt(DocumentationGenerationContext context) {
        // 自定义初始化提示词
        return "自定义的初始化内容...";
    }
}
```

### 自定义配置策略
可以通过修改`ConversationalDocumentationConfig`来调整策略：

```java
public static boolean shouldUseConversationalMode(DocumentationGenerationContext context) {
    // 自定义判断逻辑
    return customLogic(context);
}
```

## 最佳实践

1. **合理设置记忆窗口**：根据内容复杂度调整窗口大小
2. **监控API调用量**：对话模式会产生更多API调用
3. **定期清理历史**：避免内存泄漏
4. **测试不同模式**：对比两种模式的效果选择最佳方案
5. **关注文档质量**：定期评估生成文档的质量和准确性

## 故障排除

### 常见问题

1. **对话初始化失败**
   - 检查ChatMemory配置
   - 验证AI服务连接

2. **记忆功能不工作**
   - 确认MessageChatMemoryAdvisor配置正确
   - 检查对话ID是否唯一

3. **文档质量不佳**
   - 调整提示词模板
   - 增加记忆窗口大小
   - 检查输入代码的质量

4. **性能问题**
   - 考虑使用传统模式
   - 调整批次大小
   - 优化记忆管理策略
