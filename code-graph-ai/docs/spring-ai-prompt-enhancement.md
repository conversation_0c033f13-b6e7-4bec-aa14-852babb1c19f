# Spring AI Prompt 增强方案

## 概述

基于Spring AI官方Prompt API文档的学习，我们对现有的提示词构建类进行了全面改进，引入了Spring AI的标准化提示词管理方式。

## 改进前后对比

### 改进前的问题

#### 1. 硬编码字符串拼接
```java
// 原有方式 - 大量字符串拼接
return String.format("""
        # 第%d层核心流程分析
        
        请分析以下Java代码的第%d层调用关系：
        
        **入口点**: %s
        **层级**: 第%d层
        ...
        """, 
        context.getLevel(), context.getLevel(), 
        context.getEntryPointId(), context.getLevel());
```

#### 2. 缺乏模板化管理
- 提示词模板散布在Java代码中
- 难以维护和修改
- 无法进行版本控制和A/B测试

#### 3. 缺乏结构化消息
- 没有区分SystemMessage和UserMessage
- 无法利用Spring AI的消息角色机制

### 改进后的优势

#### 1. 外部化模板管理
```
src/main/resources/prompts/
├── system-base.st              # 基础系统消息模板
├── level1-analysis.st          # 第1层分析模板
├── level2-analysis.st          # 第2层分析模板
├── level3-analysis.st          # 第3层分析模板
├── conversational-init.st      # 多轮对话初始化模板
├── conversational-round.st     # 多轮对话轮次模板
└── conversational-final.st     # 多轮对话最终整合模板
```

#### 2. 使用Spring AI PromptTemplate
```java
// 新方式 - 使用PromptTemplate
@Value("classpath:/prompts/level1-analysis.st")
private Resource level1AnalysisResource;

public Prompt buildLevelAnalysisPrompt(DocumentationGenerationContext context) {
    SystemMessage systemMessage = buildSystemMessage();
    
    Map<String, Object> variables = buildLevelAnalysisVariables(context);
    PromptTemplate userTemplate = new PromptTemplate(level1AnalysisResource);
    String userContent = userTemplate.render(variables);
    UserMessage userMessage = new UserMessage(userContent);
    
    return new Prompt(List.of(systemMessage, userMessage));
}
```

#### 3. 结构化消息设计
```java
// 明确区分系统消息和用户消息
SystemMessage systemMessage = buildSystemMessage();  // 设置AI角色和基本要求
UserMessage userMessage = buildUserMessage(context); // 具体的分析任务
return new Prompt(List.of(systemMessage, userMessage));
```

## 新架构设计

### 1. SpringAIPromptBuilder（核心组件）
- 负责所有基于Spring AI PromptTemplate的提示词构建
- 使用外部化模板文件
- 支持结构化消息（SystemMessage + UserMessage）
- 提供类型安全的变量绑定

### 2. EnhancedAIPromptBuilder（兼容层）
- 保持与现有代码的向后兼容性
- 优先使用SpringAIPromptBuilder
- 提供备用方案确保稳定性
- 支持传统的字符串返回方式

### 3. ConversationalPromptBuilder（增强版）
- 委托给SpringAIPromptBuilder处理核心逻辑
- 保持现有接口不变
- 提供备用方案确保可靠性

## 模板文件示例

### 系统消息模板 (system-base.st)
```
你是一个专业的Java代码分析和文档生成助手。

## 你的职责
- 分析Java代码的结构、功能和调用关系
- 生成高质量的技术文档
- 使用中文进行回答
- 采用Markdown格式输出

## 分析要求
- 深入理解代码的业务逻辑和技术实现
- 识别关键的设计模式和架构特点
- 分析方法间的调用关系和数据流向
- 关注异常处理和边界情况
- 突出性能考虑点和优化建议

请始终保持专业、准确、详细的分析风格。
```

### 层级分析模板 (level1-analysis.st)
```
# 第1层核心流程分析

## 分析任务
请分析以下Java代码的第1层调用关系，这是从入口点 `{entryPoint}` 开始的核心业务流程。

## 项目信息
- **入口点**: {entryPoint}
- **层级**: 第{level}层
- **方法数量**: {methodCount}个
- **项目**: {projectId}
- **分支**: {branchName}

## 代码内容
{codeContent}

## 分析要求
请按以下结构进行分析：

### 1. 核心流程概述
- 整体业务流程描述
- 主要功能模块识别
- 关键业务逻辑梳理

### 2. 方法调用链分析
- 详细的调用路径
- 方法间的依赖关系
- 数据传递和转换过程

...
```

## 使用方式

### 1. 基本用法
```java
@Autowired
private SpringAIPromptBuilder springAIPromptBuilder;

// 构建完整的Prompt对象
Prompt prompt = springAIPromptBuilder.buildLevelAnalysisPrompt(context);

// 直接使用ChatClient
ChatResponse response = chatClient.call(prompt);
```

### 2. 兼容性用法
```java
@Autowired
private EnhancedAIPromptBuilder enhancedPromptBuilder;

// 保持原有接口，内部使用Spring AI方式
String promptString = enhancedPromptBuilder.buildPromptStringForLevel(context);

// 或者获取Prompt对象
Prompt prompt = enhancedPromptBuilder.buildPromptForLevel(context);
```

### 3. 多轮对话用法
```java
@Autowired
private ConversationalPromptBuilder conversationalPromptBuilder;

// 保持原有接口不变，内部使用Spring AI模板
String initPrompt = conversationalPromptBuilder.buildInitializationPrompt(context);
String roundPrompt = conversationalPromptBuilder.buildRoundAnalysisPrompt(batchContent, roundIndex, totalRounds, context);
String finalPrompt = conversationalPromptBuilder.buildFinalIntegrationPrompt(context);
```

## 技术优势

### 1. 可维护性
- **模板外部化**：提示词模板独立于Java代码
- **版本控制**：模板文件可以独立进行版本管理
- **A/B测试**：可以轻松切换不同版本的模板

### 2. 可扩展性
- **自定义模板**：支持添加新的模板文件
- **变量绑定**：类型安全的变量替换
- **条件渲染**：支持StringTemplate的条件语法

### 3. 专业性
- **结构化消息**：符合Spring AI最佳实践
- **角色分离**：明确区分系统角色和用户请求
- **标准化**：使用Spring AI官方推荐的方式

### 4. 稳定性
- **备用方案**：当Spring AI方式失败时自动回退
- **向后兼容**：保持现有接口不变
- **渐进迁移**：可以逐步迁移到新方式

## 模板语法支持

### 1. 基本变量替换
```
入口点: {entryPoint}
层级: 第{level}层
方法数量: {methodCount}个
```

### 2. 条件渲染
```
{#if previousDocumentation}
## 上一层分析总结
{previousDocumentation}
{/if}
```

### 3. 循环渲染
```
{#for method in methods}
### 方法: {method.name}
{method.description}
{/for}
```

## 迁移指南

### 阶段1：并行运行
- 保持现有代码不变
- 新增SpringAIPromptBuilder组件
- 在测试环境验证效果

### 阶段2：逐步替换
- 更新ConversationalPromptBuilder使用新方式
- 更新EnhancedAIPromptBuilder作为兼容层
- 监控性能和质量指标

### 阶段3：完全迁移
- 所有提示词构建使用Spring AI方式
- 移除旧的硬编码字符串拼接
- 优化模板文件和变量绑定

## 最佳实践

### 1. 模板设计
- **结构清晰**：使用明确的章节和层次
- **变量命名**：使用有意义的变量名
- **注释说明**：在模板中添加必要的注释

### 2. 变量管理
- **类型安全**：使用强类型的变量绑定
- **默认值**：为可选变量提供默认值
- **验证检查**：在构建前验证必需变量

### 3. 错误处理
- **备用方案**：始终提供备用的提示词构建方式
- **日志记录**：详细记录构建过程和错误信息
- **优雅降级**：确保系统在任何情况下都能工作

### 4. 性能优化
- **模板缓存**：缓存已编译的模板对象
- **变量复用**：复用通用的变量映射
- **延迟加载**：按需加载模板资源

## 总结

通过引入Spring AI的标准化Prompt管理方式，我们实现了：

1. **更好的可维护性**：模板外部化，易于修改和版本控制
2. **更高的专业性**：符合Spring AI最佳实践
3. **更强的扩展性**：支持复杂的模板语法和条件渲染
4. **更好的稳定性**：提供备用方案和向后兼容性

这个改进方案不仅提升了代码质量，还为未来的功能扩展奠定了坚实的基础。
