# 服务层Prompt生成方式更新总结

## 🎯 更新目标

将`AIDocumentationService`和`BatchDocumentationService`中的prompt生成方式更新为最新的Spring AI模板引用方式，使用`EnhancedAIPromptBuilder`和`SpringAIPromptBuilder`。

## 🔧 主要更新内容

### 1. AIDocumentationService

#### 依赖注入更新
```java
// 改进前
@Resource
private AIPromptBuilder promptBuilder;

// 改进后  
@Resource
private EnhancedAIPromptBuilder enhancedPromptBuilder;
```

#### Prompt构建方式更新
```java
// 改进前：返回字符串
String prompt = promptBuilder.buildPromptForLevel(context);
if (prompt == null || prompt.trim().isEmpty()) {
    log.error("构建提示词失败");
    return null;
}
String content = callAIServiceWithRetry(prompt);

// 改进后：使用Prompt对象
Prompt prompt = enhancedPromptBuilder.buildPromptForLevel(context);
String content = callAIServiceWithRetry(prompt);
```

#### AI服务调用方法更新
```java
// 改进前：接受字符串参数
public String callAIService(String prompt) {
    String response = chatClient.prompt()
            .user(prompt)
            .advisors(new SimpleLoggerAdvisor())
            .call()
            .content();
    return response;
}

// 改进后：接受Prompt对象
public String callAIService(Prompt prompt) {
    String response = chatClient.prompt(prompt)
            .advisors(new SimpleLoggerAdvisor())
            .call()
            .content();
    return response;
}
```

### 2. BatchDocumentationService

#### 依赖注入更新
```java
// 改进前
private final AIPromptBuilder promptBuilder;

@Autowired
public BatchDocumentationService(
        @Qualifier("simpleChatClient") ChatClient simpleChatClient,
        @Qualifier("multiConversationChatClient") ChatClient multiConversationChatClient,
        AIPromptBuilder promptBuilder, 
        ConversationalPromptBuilder conversationalPromptBuilder) {
    this.promptBuilder = promptBuilder;
    // ...
}

// 改进后
private final EnhancedAIPromptBuilder enhancedPromptBuilder;

@Autowired
public BatchDocumentationService(
        @Qualifier("simpleChatClient") ChatClient simpleChatClient,
        @Qualifier("multiConversationChatClient") ChatClient multiConversationChatClient,
        EnhancedAIPromptBuilder enhancedPromptBuilder, 
        ConversationalPromptBuilder conversationalPromptBuilder) {
    this.enhancedPromptBuilder = enhancedPromptBuilder;
    // ...
}
```

#### 分批提示词构建更新
```java
// 改进前
private List<String> buildBatchPrompts(DocumentationGenerationContext context) {
    return switch (context.getLevel()) {
        case 1 -> promptBuilder.buildLevel1BatchPrompts(context);
        case 2 -> promptBuilder.buildLevel2BatchPrompts(context);
        case 3 -> promptBuilder.buildLevel3BatchPrompts(context);
        default -> {
            log.error("不支持的层级: {}", context.getLevel());
            yield List.of();
        }
    };
}

// 改进后
private List<String> buildBatchPrompts(DocumentationGenerationContext context) {
    return switch (context.getLevel()) {
        case 1 -> enhancedPromptBuilder.buildLevel1BatchPrompts(context);
        case 2 -> enhancedPromptBuilder.buildLevel2BatchPrompts(context);
        case 3 -> enhancedPromptBuilder.buildLevel3BatchPrompts(context);
        default -> {
            log.error("不支持的层级: {}", context.getLevel());
            yield List.of();
        }
    };
}
```

#### 模型配置获取更新
```java
// 改进前
ModelConfig modelConfig = promptBuilder.getModelConfig();

// 改进后
ModelConfig modelConfig = enhancedPromptBuilder.getModelConfig();
```

### 3. EnhancedAIPromptBuilder 清理

同时清理了`EnhancedAIPromptBuilder`中剩余的try-catch逻辑：

```java
// 改进前：包含try-catch
public List<String> buildLevel1BatchPrompts(DocumentationGenerationContext context) {
    try {
        List<MethodInfo> level1Methods = context.getSubgraph().getMethodsAtLevel(1);
        // ... 处理逻辑
        return prompts;
    } catch (Exception e) {
        log.error("构建第1层分批提示词时发生错误", e);
        return List.of();
    }
}

// 改进后：直接处理，异常向上传播
public List<String> buildLevel1BatchPrompts(DocumentationGenerationContext context) {
    List<MethodInfo> level1Methods = context.getSubgraph().getMethodsAtLevel(1);
    // ... 处理逻辑
    return prompts;
}
```

## 📊 更新效果

### 1. 架构一致性 ✅
- 所有服务层都使用统一的prompt构建方式
- 基于Spring AI的标准化模板系统
- 消除了不同服务间的实现差异

### 2. 类型安全性 ✅
```java
// 改进前：字符串传递，容易出错
String prompt = promptBuilder.buildPromptForLevel(context);
if (prompt == null) { /* 需要手动检查 */ }

// 改进后：Prompt对象，类型安全
Prompt prompt = enhancedPromptBuilder.buildPromptForLevel(context);
// 如果构建失败会直接抛异常，无需手动检查
```

### 3. 功能完整性 ✅
- `AIDocumentationService`：支持完整的Prompt对象和结构化消息
- `BatchDocumentationService`：分批处理仍使用字符串（合理设计）
- 多轮对话：已经使用最新的模板引用方式

### 4. 错误处理一致性 ✅
- 统一采用Fail Fast原则
- 移除了所有fallback逻辑
- 异常信息更加明确和有用

## 🎯 设计优势

### 1. 模板复用最大化
```java
// 所有服务都使用相同的模板系统
AIDocumentationService -> EnhancedAIPromptBuilder -> SpringAIPromptBuilder -> 共享模板片段
BatchDocumentationService -> EnhancedAIPromptBuilder -> SpringAIPromptBuilder -> 共享模板片段
```

### 2. 维护成本最小化
- 修改模板只需更新共享片段
- 所有服务自动获得更新
- 统一的错误处理策略

### 3. 扩展性最大化
- 新增服务可以直接使用现有的prompt构建器
- 支持新的层级和场景
- 灵活的模板组合机制

## 🔄 调用链路

### 单次文档生成
```
AIDocumentationService
    ↓
EnhancedAIPromptBuilder.buildPromptForLevel()
    ↓
SpringAIPromptBuilder.buildLevelAnalysisPrompt()
    ↓
模板引用系统 (shared/document-structure.st + shared/analysis-requirements-levelX.st)
    ↓
Prompt对象 (SystemMessage + UserMessage)
    ↓
ChatClient.prompt(prompt)
```

### 分批文档生成
```
BatchDocumentationService
    ↓
EnhancedAIPromptBuilder.buildLevelXBatchPrompts()
    ↓
传统字符串拼接 (适合分批场景)
    ↓
List<String> 批次提示词
    ↓
并行处理：ChatClient.prompt().user(stringPrompt)
```

### 多轮对话文档生成
```
BatchDocumentationService
    ↓
ConversationalPromptBuilder
    ↓
SpringAIPromptBuilder (conversational模板)
    ↓
模板引用系统 (shared/document-structure.st + shared/integration-requirements.st)
    ↓
串行处理：ChatClient with Memory
```

## 🎉 总结

通过这次更新，我们实现了：

1. **架构统一**：所有服务层都使用相同的prompt构建方式
2. **类型安全**：从字符串传递升级到Prompt对象
3. **模板复用**：最大化利用共享模板片段
4. **错误处理**：统一的Fail Fast策略
5. **维护简化**：单点修改，全局生效

现在整个系统的prompt生成方式完全统一，基于Spring AI的最佳实践，具有更好的可维护性、扩展性和稳定性。
