package com.puti.code.ai.documentation;

import com.puti.code.base.model.MethodInfo;
import com.puti.code.base.model.SubgraphData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 多轮对话文档生成测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ConversationalDocumentationTest {

    @Mock
    private AIPromptBuilder promptBuilder;

    @Mock
    private ConversationalPromptBuilder conversationalPromptBuilder;

    @Mock
    private ChatClient chatClient;

    @Mock
    private ChatMemory chatMemory;

    @Mock
    private SubgraphData subgraphData;

    private BatchDocumentationService batchDocumentationService;

    @BeforeEach
    void setUp() {
        batchDocumentationService = new BatchDocumentationService(
                promptBuilder, conversationalPromptBuilder, chatClient, chatMemory);
    }

    @Test
    void testConversationalModeSelection() {
        // 测试第1层 - 应该使用传统模式
        DocumentationGenerationContext level1Context = DocumentationGenerationContext.builder()
                .entryPointId("com.example.Service.method1")
                .level(1)
                .subgraph(createMockSubgraph(5))
                .build();

        // 测试第2层 - 应该使用对话模式
        DocumentationGenerationContext level2Context = DocumentationGenerationContext.builder()
                .entryPointId("com.example.Service.method1")
                .level(2)
                .subgraph(createMockSubgraph(20))
                .build();

        // 验证配置逻辑
        assertFalse(ConversationalDocumentationConfig.shouldUseConversationalForLevel(1));
        assertTrue(ConversationalDocumentationConfig.shouldUseConversationalForLevel(2));
        assertTrue(ConversationalDocumentationConfig.shouldUseConversationalForLevel(3));
    }

    @Test
    void testContentBasedModeSelection() {
        // 测试基于内容复杂度的模式选择
        assertTrue(ConversationalDocumentationConfig.shouldUseConversationalForContent(20, 60000));
        assertFalse(ConversationalDocumentationConfig.shouldUseConversationalForContent(5, 10000));
    }

    @Test
    void testConversationalPromptBuilding() {
        DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                .entryPointId("com.example.Service.method1")
                .level(2)
                .projectId("test-project")
                .branchName("main")
                .subgraph(createMockSubgraph(15))
                .build();

        ConversationalPromptBuilder builder = new ConversationalPromptBuilder();

        // 测试初始化提示词
        String initPrompt = builder.buildInitializationPrompt(context);
        assertNotNull(initPrompt);
        assertTrue(initPrompt.contains("第2层"));
        assertTrue(initPrompt.contains("com.example.Service.method1"));
        assertTrue(initPrompt.contains("test-project"));

        // 测试轮次分析提示词
        String roundPrompt = builder.buildRoundAnalysisPrompt("测试代码内容", 0, 3, context);
        assertNotNull(roundPrompt);
        assertTrue(roundPrompt.contains("第1轮"));
        assertTrue(roundPrompt.contains("共3轮"));

        // 测试最终整合提示词
        String finalPrompt = builder.buildFinalIntegrationPrompt(context);
        assertNotNull(finalPrompt);
        assertTrue(finalPrompt.contains("最终文档整合"));
        assertTrue(finalPrompt.contains("第2层"));
    }

    @Test
    void testConfigValidation() {
        // 测试配置验证
        assertTrue(ConversationalDocumentationConfig.validateConfig());
    }

    @Test
    void testBatchCountSuggestion() {
        DocumentationGenerationContext smallContext = DocumentationGenerationContext.builder()
                .level(2)
                .subgraph(createMockSubgraph(8))
                .build();

        DocumentationGenerationContext largeContext = DocumentationGenerationContext.builder()
                .level(3)
                .subgraph(createMockSubgraph(50))
                .build();

        int smallBatchCount = ConversationalDocumentationConfig.getSuggestedBatchCount(smallContext);
        int largeBatchCount = ConversationalDocumentationConfig.getSuggestedBatchCount(largeContext);

        assertTrue(smallBatchCount >= 2);
        assertTrue(largeBatchCount > smallBatchCount);
        assertTrue(largeBatchCount <= ConversationalDocumentationConfig.getMaxConversationRounds());
    }

    @Test
    void testConversationIdGeneration() {
        DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                .entryPointId("com.example.Service.method1")
                .level(2)
                .build();

        // 使用反射或者将方法设为包可见来测试
        // 这里简化测试，主要验证ID格式
        String conversationId = String.format("doc_gen_%s_level_%d_%d", 
                context.getEntryPointId(), 
                context.getLevel(), 
                System.currentTimeMillis());

        assertTrue(conversationId.contains("doc_gen_"));
        assertTrue(conversationId.contains("com.example.Service.method1"));
        assertTrue(conversationId.contains("level_2"));
    }

    /**
     * 创建模拟的SubgraphData
     */
    private SubgraphData createMockSubgraph(int methodCount) {
        SubgraphData mockSubgraph = mock(SubgraphData.class);
        
        List<MethodInfo> methods = createMockMethods(methodCount);
        
        when(mockSubgraph.getMethodsAtLevel(1)).thenReturn(methods.subList(0, Math.min(5, methodCount)));
        when(mockSubgraph.getMethodsAtLevel(2)).thenReturn(methods.subList(0, Math.min(methodCount, methodCount)));
        when(mockSubgraph.getAllMethods()).thenReturn(methods);
        when(mockSubgraph.getTotalNodes()).thenReturn(methodCount);
        when(mockSubgraph.getTotalEdges()).thenReturn(methodCount * 2);
        when(mockSubgraph.isEmpty()).thenReturn(methodCount == 0);
        
        return mockSubgraph;
    }

    /**
     * 创建模拟的MethodInfo列表
     */
    private List<MethodInfo> createMockMethods(int count) {
        return java.util.stream.IntStream.range(0, count)
                .mapToObj(i -> {
                    MethodInfo method = new MethodInfo();
                    method.setSimpleIdentifier("method" + i);
                    method.setDescription("测试方法 " + i + " 的描述");
                    method.setContent("public void method" + i + "() { /* 测试代码 */ }");
                    return method;
                })
                .toList();
    }

    @Test
    void testPromptBuilderBatchSummary() {
        ConversationalPromptBuilder builder = new ConversationalPromptBuilder();
        
        String batchContent = """
                public class TestService {
                    public void method1() {
                        // 实现逻辑
                    }
                    
                    private String method2() {
                        return "test";
                    }
                }
                """;
        
        String summary = builder.buildBatchSummary(batchContent);
        assertNotNull(summary);
        assertTrue(summary.contains("内容行数"));
        assertTrue(summary.contains("估计方法数"));
    }

    @Test
    void testIntermediateSummaryPrompt() {
        ConversationalPromptBuilder builder = new ConversationalPromptBuilder();
        
        String summaryPrompt = builder.buildIntermediateSummaryPrompt(3, 5);
        assertNotNull(summaryPrompt);
        assertTrue(summaryPrompt.contains("已经完成了3轮"));
        assertTrue(summaryPrompt.contains("共5轮"));
        assertTrue(summaryPrompt.contains("中间总结"));
    }

    @Test
    void testContextRebuildPrompt() {
        ConversationalPromptBuilder builder = new ConversationalPromptBuilder();
        
        String previousSummary = "之前分析了服务层的核心方法";
        String rebuildPrompt = builder.buildContextRebuildPrompt(previousSummary, 4, 6);
        
        assertNotNull(rebuildPrompt);
        assertTrue(rebuildPrompt.contains("第4轮"));
        assertTrue(rebuildPrompt.contains("共6轮"));
        assertTrue(rebuildPrompt.contains(previousSummary));
        assertTrue(rebuildPrompt.contains("上下文恢复"));
    }
}
