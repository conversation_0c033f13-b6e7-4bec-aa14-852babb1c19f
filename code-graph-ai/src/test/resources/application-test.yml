# Spring AI 测试配置
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:test-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 4000
      embedding:
        options:
          model: text-embedding-ada-002

# 日志配置
logging:
  level:
    com.puti.code.ai: DEBUG
    org.springframework.ai: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 测试环境配置
test:
  skip-network-tests: true
