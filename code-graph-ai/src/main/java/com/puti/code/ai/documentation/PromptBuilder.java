package com.puti.code.ai.documentation;

import com.puti.code.ai.config.BatchProcessingConfig;
import com.puti.code.ai.support.TokenSupport;
import com.puti.code.base.model.MethodInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.model.Content;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.stringtemplate.v4.ST;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于Spring AI PromptTemplate的提示词构建器
 * 使用外部化模板和结构化消息构建高质量提示词
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromptBuilder {

    // 系统消息模板资源
    @Value("classpath:/prompts/system-base.st")
    private Resource systemBaseResource;

    // 各层级分析模板资源（现在只包含引用逻辑）
    @Value("classpath:/prompts/level1-analysis.st")
    private Resource level1AnalysisResource;

    @Value("classpath:/prompts/level2-analysis.st")
    private Resource level2AnalysisResource;

    @Value("classpath:/prompts/level3-analysis.st")
    private Resource level3AnalysisResource;

    // 共享模板片段资源
    @Value("classpath:/prompts/shared/document-structure.st")
    private Resource documentStructureResource;

    @Value("classpath:/prompts/shared/analysis-requirements-level1.st")
    private Resource level1RequirementsResource;

    @Value("classpath:/prompts/shared/analysis-requirements-level2.st")
    private Resource level2RequirementsResource;

    @Value("classpath:/prompts/shared/analysis-requirements-level3.st")
    private Resource level3RequirementsResource;

    @Value("classpath:/prompts/shared/integration-requirements.st")
    private Resource integrationRequirementsResource;

    // 多轮对话模板资源
    @Value("classpath:/prompts/conversational-init.st")
    private Resource conversationalInitResource;

    @Value("classpath:/prompts/conversational-round.st")
    private Resource conversationalRoundResource;

    @Value("classpath:/prompts/conversational-final.st")
    private Resource conversationalFinalResource;

    // 批次分析模板资源
    @Value("classpath:/prompts/shared/batch-analysis-level1.st")
    private Resource batchAnalysisLevel1Resource;

    @Value("classpath:/prompts/shared/batch-analysis-level2.st")
    private Resource batchAnalysisLevel2Resource;

    @Value("classpath:/prompts/shared/batch-analysis-level3.st")
    private Resource batchAnalysisLevel3Resource;

    /**
     * 构建系统消息
     */
    public SystemMessage buildSystemMessage() {
        PromptTemplate systemTemplate = new PromptTemplate(systemBaseResource);
        return new SystemMessage(systemTemplate.render());
    }

    /**
     * 构建层级分析的完整Prompt
     */
    public Prompt buildLevelAnalysisPrompt(DocumentationGenerationContext context) {
        SystemMessage systemMessage = buildSystemMessage();
        UserMessage userMessage = buildLevelAnalysisUserMessage(context);

        return new Prompt(List.of(systemMessage, userMessage));
    }

    /**
     * 构建层级分析的用户消息
     * 使用模板引用方式，将共享片段注入到主模板中
     */
    private UserMessage buildLevelAnalysisUserMessage(DocumentationGenerationContext context) {
        // 获取层级特定的模板资源
        Resource templateResource = switch (context.getLevel()) {
            case 1 -> level1AnalysisResource;
            case 2 -> level2AnalysisResource;
            case 3 -> level3AnalysisResource;
            default -> level2AnalysisResource;
        };

        // 构建变量映射，包含共享片段的内容
        Map<String, Object> variables = buildLevelAnalysisVariables(context);

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));
        variables.put("analysisRequirements", renderTemplate(getLevelRequirementsResource(context.getLevel()), variables));

        // 渲染主模板
        PromptTemplate mainTemplate = new PromptTemplate(templateResource);
        String userContent = mainTemplate.render(variables);

        return new UserMessage(userContent);
    }

    /**
     * 构建层级分析的模板变量
     */
    private Map<String, Object> buildLevelAnalysisVariables(DocumentationGenerationContext context) {
        Map<String, Object> variables = new HashMap<>();

        variables.put("entryPoint", context.getEntryPointId());
        variables.put("level", context.getLevel());
        variables.put("methodCount", context.getMethodCountForLevel());
        variables.put("projectId", context.getProjectId() != null ? context.getProjectId() : "未指定");
        variables.put("branchName", context.getBranchName() != null ? context.getBranchName() : "未指定");
        variables.put("codeContent", buildCodeContent(context));

        // 如果有前一层文档，添加到变量中
        if (context.getPreviousDocumentation() != null) {
            variables.put("previousDocumentation", context.getPreviousDocumentation().getContent());
        }

        return variables;
    }

    /**
     * 构建多轮对话初始化Prompt
     */
    public Prompt buildConversationalInitPrompt(DocumentationGenerationContext context, int batchCount) {
        SystemMessage systemMessage = buildSystemMessage();
        UserMessage userMessage = buildConversationalInitUserMessage(context, batchCount);

        return new Prompt(List.of(systemMessage, userMessage));
    }

    /**
     * 构建多轮对话初始化的用户消息
     */
    private UserMessage buildConversationalInitUserMessage(DocumentationGenerationContext context, int batchCount) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("entryPoint", context.getEntryPointId());
        variables.put("level", context.getLevel());
        variables.put("methodCount", context.getMethodCountForLevel());
        variables.put("projectId", context.getProjectId() != null ? context.getProjectId() : "未指定");
        variables.put("branchName", context.getBranchName() != null ? context.getBranchName() : "未指定");
        variables.put("batchCount", batchCount);

        PromptTemplate userTemplate = new PromptTemplate(conversationalInitResource);
        String userContent = userTemplate.render(variables);

        return new UserMessage(userContent);
    }

    /**
     * 构建多轮对话轮次分析Prompt
     */
    public Prompt buildConversationalRoundPrompt(String batchContent, int roundIndex, int totalRounds,
                                                 DocumentationGenerationContext context) {
        // 对于多轮对话，不需要重复系统消息，因为ChatMemory会管理
        UserMessage userMessage = buildConversationalRoundUserMessage(
                batchContent, roundIndex, totalRounds, context);

        return new Prompt(List.of(userMessage));
    }

    /**
     * 构建多轮对话轮次分析的用户消息
     * 使用模板引用方式，将共享片段注入到主模板中
     */
    private UserMessage buildConversationalRoundUserMessage(String batchContent, int roundIndex,
                                                            int totalRounds, DocumentationGenerationContext context) {
        // 构建变量映射，包含共享片段的内容
        Map<String, Object> variables = buildLevelAnalysisVariables(context);
        variables.put("roundIndex", roundIndex + 1); // 显示从1开始
        variables.put("totalRounds", totalRounds);
        variables.put("codeContent", batchContent);

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));

        // 渲染主模板
        PromptTemplate userTemplate = new PromptTemplate(conversationalRoundResource);
        String userContent = userTemplate.render(variables);

        return new UserMessage(userContent);
    }

    /**
     * 构建多轮对话最终整合Prompt
     */
    public Prompt buildConversationalFinalPrompt(DocumentationGenerationContext context) {
        UserMessage userMessage = buildConversationalFinalUserMessage(context);
        return new Prompt(List.of(userMessage));
    }

    /**
     * 构建多轮对话最终整合的用户消息
     * 使用模板引用方式，复用共享的文档结构
     */
    private UserMessage buildConversationalFinalUserMessage(DocumentationGenerationContext context) {
        Map<String, Object> variables = buildLevelAnalysisVariables(context);

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));
        variables.put("integrationRequirements", renderTemplate(integrationRequirementsResource, variables));

        // 渲染多轮对话最终模板
        PromptTemplate finalTemplate = new PromptTemplate(conversationalFinalResource);
        String userContent = finalTemplate.render(variables);

        return new UserMessage(userContent);
    }

    /**
     * 构建代码内容字符串
     */
    private String buildCodeContent(DocumentationGenerationContext context) {
        if (context.getSubgraph() == null) {
            return "无代码内容";
        }

        List<MethodInfo> methods = switch (context.getLevel()) {
            case 1 -> context.getSubgraph().getMethodsAtLevel(1);
            case 2 -> context.getSubgraph().getMethodsAtLevel(2);
            default -> context.getSubgraph().getAllMethods();
        };

        StringBuilder codeContent = new StringBuilder();
        appendMethodsContent(codeContent, methods);
        return codeContent.toString();
    }

    /**
     * 获取层级对应的分析要求资源
     */
    private Resource getLevelRequirementsResource(int level) {
        return switch (level) {
            case 1 -> level1RequirementsResource;
            case 2 -> level2RequirementsResource;
            default -> level3RequirementsResource;
        };
    }

    /**
     * 渲染模板片段
     */
    private String renderTemplate(Resource templateResource, Map<String, Object> variables) {
        PromptTemplate template = new PromptTemplate(templateResource);
        return template.render(variables);
    }

    /**
     * 为第1层构建分批提示词
     */
    public List<String> buildLevel1BatchPrompts(DocumentationGenerationContext context) {
        List<MethodInfo> level1Methods = context.getSubgraph().getMethodsAtLevel(1);
        List<List<MethodInfo>> batches = splitMethodsIntoBatches(level1Methods, context.getLevel());

        List<String> prompts = new ArrayList<>();
        for (int i = 0; i < batches.size(); i++) {
            String prompt = buildLevel1BatchPrompt(context, batches.get(i), i, batches.size());
            prompts.add(prompt);
        }

        return prompts;
    }

    /**
     * 为第2层构建分批提示词
     */
    public List<String> buildLevel2BatchPrompts(DocumentationGenerationContext context) {
        List<MethodInfo> level2Methods = context.getSubgraph().getMethodsAtLevel(2);
        List<List<MethodInfo>> batches = splitMethodsIntoBatches(level2Methods, context.getLevel());

        List<String> prompts = new ArrayList<>();
        for (int i = 0; i < batches.size(); i++) {
            String prompt = buildLevel2BatchPrompt(context, batches.get(i), i, batches.size());
            prompts.add(prompt);
        }

        return prompts;
    }

    /**
     * 为第3层构建分批提示词
     */
    public List<String> buildLevel3BatchPrompts(DocumentationGenerationContext context) {
        List<MethodInfo> level3Methods = context.getSubgraph().getAllMethods();
        List<List<MethodInfo>> batches = splitMethodsIntoBatches(level3Methods, context.getLevel());

        List<String> prompts = new ArrayList<>();
        for (int i = 0; i < batches.size(); i++) {
            String prompt = buildLevel3BatchPrompt(context, batches.get(i), i, batches.size());
            prompts.add(prompt);
        }

        return prompts;
    }

    /**
     * 构建第1层批次提示词
     * 使用模板引用方式，将共享片段注入到主模板中
     */
    private String buildLevel1BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> methods,
                                          int batchIndex, int totalBatches) {
        // 构建变量映射，包含共享片段的内容
        Map<String, Object> variables = buildLevelAnalysisVariables(context);
        variables.put("batchIndex", batchIndex + 1); // 显示从1开始
        variables.put("totalBatches", totalBatches);

        // 构建批次描述
        String batchDescription = (batchIndex == 0)
            ? "这是第1层调用关系的核心流程分析。请重点关注主要业务流程和关键调用路径。"
            : "这是第1层调用关系分析的后续批次。请结合之前的分析，继续完善整体理解。";
        variables.put("batchDescription", batchDescription);

        // 构建代码内容
        StringBuilder codeContent = new StringBuilder();
        appendMethodsContent(codeContent, methods);
        variables.put("codeContent", codeContent.toString());

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));
        variables.put("analysisRequirements", renderTemplate(level1RequirementsResource, variables));

        // 渲染主模板
        PromptTemplate batchTemplate = new PromptTemplate(batchAnalysisLevel1Resource);
        return batchTemplate.render(variables);
    }

    /**
     * 构建第2层批次提示词
     * 使用模板引用方式，将共享片段注入到主模板中
     */
    private String buildLevel2BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> methods,
                                          int batchIndex, int totalBatches) {
        // 构建变量映射，包含共享片段的内容
        Map<String, Object> variables = buildLevelAnalysisVariables(context);
        variables.put("batchIndex", batchIndex + 1); // 显示从1开始
        variables.put("totalBatches", totalBatches);

        // 构建批次描述
        String batchDescription = "这是第2层详细流程分析。请深入分析详细实现、技术特点和与第1层的关系。";
        variables.put("batchDescription", batchDescription);

        // 构建前置文档摘要
        String previousDocumentationSummary = "";
        if (context.getPreviousDocumentation() != null) {
            previousDocumentationSummary = "## 第1层分析总结\n" +
                context.getPreviousDocumentation().getContent().substring(0,
                    Math.min(500, context.getPreviousDocumentation().getContent().length())) + "...\n\n";
        }
        variables.put("previousDocumentationSummary", previousDocumentationSummary);

        // 构建代码内容
        StringBuilder codeContent = new StringBuilder();
        appendMethodsContent(codeContent, methods);
        variables.put("codeContent", codeContent.toString());

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));
        variables.put("analysisRequirements", renderTemplate(level2RequirementsResource, variables));

        // 渲染主模板
        PromptTemplate batchTemplate = new PromptTemplate(batchAnalysisLevel2Resource);
        return batchTemplate.render(variables);
    }

    /**
     * 构建第3层批次提示词
     * 使用模板引用方式，将共享片段注入到主模板中
     */
    private String buildLevel3BatchPrompt(DocumentationGenerationContext context, List<MethodInfo> methods,
                                          int batchIndex, int totalBatches) {
        // 构建变量映射，包含共享片段的内容
        Map<String, Object> variables = buildLevelAnalysisVariables(context);
        variables.put("batchIndex", batchIndex + 1); // 显示从1开始
        variables.put("totalBatches", totalBatches);

        // 构建批次描述
        String batchDescription = "这是第3层完整系统分析。请全面分析实现细节、完整调用链路和系统架构。";
        variables.put("batchDescription", batchDescription);

        // 构建前置文档摘要（第3层可能有前面层级的文档）
        String previousDocumentationSummary = "";
        if (context.getPreviousDocumentation() != null) {
            previousDocumentationSummary = "## 前面层级分析总结\n" +
                context.getPreviousDocumentation().getContent().substring(0,
                    Math.min(500, context.getPreviousDocumentation().getContent().length())) + "...\n\n";
        }
        variables.put("previousDocumentationSummary", previousDocumentationSummary);

        // 构建代码内容
        StringBuilder codeContent = new StringBuilder();
        appendMethodsContent(codeContent, methods);
        variables.put("codeContent", codeContent.toString());

        // 渲染共享片段
        variables.put("documentStructure", renderTemplate(documentStructureResource, variables));
        variables.put("analysisRequirements", renderTemplate(level3RequirementsResource, variables));

        // 渲染主模板
        PromptTemplate batchTemplate = new PromptTemplate(batchAnalysisLevel3Resource);
        return batchTemplate.render(variables);
    }

    private List<List<MethodInfo>> splitMethodsIntoBatches(List<MethodInfo> methods, int level) {
        int maxTokens = BatchProcessingConfig.getMaxTokensForLevel(level);
        int maxTokensForLevel = BatchProcessingConfig.getMaxTokensForLevel(Integer.MAX_VALUE);

        Map<String, Integer> methodInfoTokenMap = methods.stream().collect(Collectors.toMap(MethodInfo::getMethodId,
                e -> TokenSupport.calculateTokens(e, ModelConfig.getDefaultConfig().getEncodingType()), (s1, s2) -> s2));
        List<MethodInfo> methodInfoOrderList = methods.stream().sorted(Comparator.comparingInt(e -> methodInfoTokenMap.get(e.getMethodId())))
                .toList();

        List<List<MethodInfo>> batches = new ArrayList<>();
        List<MethodInfo> currentBatch = new ArrayList<>();
        int currentBatchTokens = 0;

        for (MethodInfo method : methodInfoOrderList) {
            String methodId = method.getMethodId();
            int methodTokens = methodInfoTokenMap.get(methodId);

            // 检查方法是否超出最大允许token限制
            if (methodTokens > maxTokensForLevel) {
                log.error("方法 {} 的token数({})超出了最大允许限制({}), 跳过该方法",
                    methodId, methodTokens, maxTokensForLevel);
                continue;
            }

            // 检查是否需要开启新分组
            if (currentBatchTokens + methodTokens > maxTokens) {
                if (currentBatchTokens <= maxTokensForLevel) {
                    // 当前分组还在容忍范围内，方法加入当前分组，然后开启下一个分组
                    currentBatch.add(method);

                    // 保存当前分组并开启新分组
                    batches.add(new ArrayList<>(currentBatch));
                    currentBatch.clear();
                    currentBatchTokens = 0;
                } else {
                    // 当前分组已经超过maxTokensForLevel，方法进入下一个分组
                    // 先保存当前分组
                    if (!currentBatch.isEmpty()) {
                        batches.add(new ArrayList<>(currentBatch));
                        currentBatch.clear();
                    }

                    // 方法进入新分组
                    currentBatch.add(method);
                    currentBatchTokens = methodTokens;
                }
            } else {
                // 方法可以加入当前分组而不超过maxTokens
                currentBatch.add(method);
                currentBatchTokens += methodTokens;
            }
        }

        // 添加最后一个分组（如果不为空）
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        log.info("方法分组完成，共{}个方法分为{}个批次，每批次token限制: {}, 最大允许token: {}",
            methods.size(), batches.size(), maxTokens, maxTokensForLevel);

        return batches;
    }

    /**
     * 添加方法内容到提示词
     */
    private void appendMethodsContent(StringBuilder prompt, List<MethodInfo> methods) {
        for (int i = 0; i < methods.size(); i++) {
            MethodInfo method = methods.get(i);
            prompt.append(String.format("### 方法 %d: %s\n\n", i + 1,
                    method.getSimpleIdentifier() != null ? method.getSimpleIdentifier() : "未知方法"));

            if (method.getDescription() != null && !method.getDescription().trim().isEmpty()) {
                prompt.append("**描述**: ").append(method.getDescription()).append("\n\n");
            }

            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                prompt.append("**代码**:\n```java\n")
                        .append(method.getContent())
                        .append("\n```\n\n");
            }

            prompt.append("---\n\n");
        }
    }


    public String buildInitializationPromptString(DocumentationGenerationContext context, int batchCount) {
        Prompt prompt = buildConversationalInitPrompt(context, batchCount);

        return extractUserMessageContent(prompt);
    }

    public String buildRoundAnalysisPromptString(String batchContent, int roundIndex, int totalRounds,
                                                 DocumentationGenerationContext context) {
        Prompt prompt = buildConversationalRoundPrompt(batchContent, roundIndex, totalRounds, context);
        return extractUserMessageContent(prompt);
    }

    public String buildFinalIntegrationPromptString(DocumentationGenerationContext context) {
        Prompt prompt = buildConversationalFinalPrompt(context);
        return extractUserMessageContent(prompt);
    }

    /**
     * 从Prompt中提取用户消息内容
     */
    private String extractUserMessageContent(Prompt prompt) {
        return prompt.getInstructions().stream()
                .filter(message -> message instanceof UserMessage)
                .map(Content::getText)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("未找到用户消息内容"));
    }

    public static void main(String[] args) {
        String template = """
                {if(previousDocumentation)}
                ## 上一层分析总结
                {previousDocumentation}
                {endif}
                """;
        ST st = new ST(template, '{', '}');
//        st.add("previousDocumentation", "1111");
        System.out.println(st.render());
    }
}
