# 多轮对话文档生成任务初始化

你好！我需要通过多轮对话的方式为一个Java项目生成完整的技术文档。

## 项目基本信息
- **入口点方法**: {entryPoint}
- **分析层级**: 第{level}层
- **总方法数**: {methodCount}个
- **项目ID**: {projectId}
- **分支**: {branchName}

## 多轮对话流程说明
我将通过以下方式与你协作：

### 第一阶段：分批分析
1. **批次化处理**: 我会将代码分成{batchCount}个批次
2. **逐批分析**: 每轮对话分析一个批次的代码
3. **上下文保持**: 你需要记住之前轮次的分析结果
4. **渐进构建**: 基于历史分析，逐步完善理解

### 第二阶段：整合生成
1. **全局整合**: 基于所有批次的分析结果
2. **文档生成**: 生成完整、连贯的技术文档
3. **质量保证**: 确保逻辑清晰、结构合理

## 分析重点
请在每轮分析中关注：
- **功能职责**: 每个方法/类的具体功能
- **调用关系**: 方法间的调用链路和依赖
- **技术特点**: 使用的技术栈、设计模式、算法
- **业务逻辑**: 核心业务规则和处理流程
- **异常处理**: 错误处理和边界情况
- **性能考虑**: 潜在瓶颈和优化点

## 输出要求
- **语言**: 使用中文
- **格式**: Markdown格式
- **结构**: 清晰的层次结构
- **详细度**: 提供足够的技术细节
- **连贯性**: 与之前分析保持一致

## 协作方式
- **确认理解**: 请确认你理解了这个任务
- **保持记忆**: 记住每轮的分析要点
- **主动关联**: 主动识别与之前分析的关联
- **质疑澄清**: 如有疑问请及时提出

请确认你理解了这个多轮对话文档生成任务，我们即将开始第一轮代码分析。
